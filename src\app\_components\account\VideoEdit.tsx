"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Sport,
  type UserRole,
  VideoPermission,
  VideoStatus,
  videoPermissions,
} from "~/lib/enums";
import { videoEditFormSchema, type Competition } from "~/lib/formSchemas";
import type { AccountVideoOutput } from "~/server/api/routers/account";
import { Button } from "~/components/ui/button";
import { useEffect, useState } from "react";
import { generateVideoThumbnail, getThumnailParams } from "~/server/utils/mux";
import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { UploadButton } from "../UploadButton";
import { useRouter } from "next/navigation";
import { AthleteSelect } from "../AthleteSelect";
import { useSession } from "next-auth/react";
import { getUserSports } from "~/lib/roles";
import type { MuxVideoFile } from "~/lib/interface";
import { Metadata } from "../Metadata";
import { CompetitionSelect } from "../CompetitionSelect";
import { Tag } from "../Tag";
import type { VideoUpdateInput } from "~/server/api/routers/video";
import { Label } from "~/components/ui/label";
import { VideoDate } from "../VideoDate";
import { X } from "lucide-react";

type FormProps = z.infer<typeof videoEditFormSchema>;

export const AccountVideoEdit = ({ video }: { video?: AccountVideoOutput }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const router = useRouter();
  const { data: session } = useSession();
  const roleSports = getUserSports((session?.user.roles ?? []) as UserRole[]);

  // Check if this video is a child video (has a parentId)
  const isChildVideo = video?.parentId != null;

  const [mux, setMux] = useState<MuxVideoFile | undefined>(
    video
      ? {
          muxAssetId: video.muxAssetId ?? "",
          muxPlaybackId: video.muxPlaybackId ?? "",
          fullFilename: video.fullFilename ?? "",
          filename: video.filename ?? "",
          thumbnailToken: video.thumbnailToken ?? "",
          playbackToken: video.playbackToken ?? "",
          storyboardToken: video.storyboardToken ?? "",
          duration: video.duration ?? 0,
          maxHeight: video.maxHeight ?? 0,
          maxWidth: video.maxWidth ?? 0,
        }
      : undefined,
  );

  const { time } = getThumnailParams(video?.thumbnail);

  const storedCompetition = video?.competition;

  const [videoPauseTime, setVideoPauseTime] = useState<number>(0);
  const [selectedAthletes, setSelectedAthletes] = useState<
    VideoUpdateInput["athletes"]
  >(
    video?.athletes.map((x) => ({
      athleteId: x.athleteId,
      name: x.name ?? "unknow",
      isHp: x.isHp,
    })),
  );
  const [selectedTags, setSelectedTags] = useState<string[]>(
    video?.tags.map((x) => x.text) ?? [],
  );

  const [selectedCompetition, setSelectedCompetition] = useState<
    Competition | undefined
  >(undefined);

  // Get linked videos for this video
  const { data: linkedVideosData, refetch: refetchLinkedVideos } =
    api.video.getLinkedVideos.useQuery(
      {
        parentId: video?.id ?? "",
      },
      { enabled: !!video?.id },
    );

  const linkedVideos = linkedVideosData?.videoList ?? [];

  const { mutate: removeRelationship } =
    api.video.removeVideoRelationship.useMutation({
      onSuccess: () => {
        toast.success("Relationship removed");
        void refetchLinkedVideos();
      },
      onError: (error) => {
        toast.error(error.message || "Failed to remove relationship");
      },
    });

  useEffect(() => {
    setSelectedCompetition(storedCompetition ?? undefined);
  }, [storedCompetition]);

  const { mutate: upsert, isPending: isPendingUpdate } =
    api.video.upsert.useMutation({
      onSuccess: ({ id }) => {
        toast.success("Video updated");
        if (video) {
          router.refresh();
        } else {
          router.replace(`/account/videos/${id}`);
        }
      },
      onError: () => toast.error("Failed to update video"),
    });

  const form = useForm<z.infer<typeof videoEditFormSchema>>({
    resolver: zodResolver(videoEditFormSchema),
    defaultValues: {
      title: video?.title ?? "",
      sport: video?.sport ?? Sport.cycling,
      permission: video?.permission ?? VideoPermission.public,
      status: video?.status ?? VideoStatus.Raw,
      time: time?.toString() ?? "",
      videoDate: video?.videoDate ?? undefined,
    },
  });

  const title = form.watch("title");

  useEffect(() => {
    if (mux && (!title || title === "")) {
      form.setValue("title", mux.filename);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mux]);

  const onSubmit = async (values: FormProps) => {
    if (!mux?.muxPlaybackId) return toast.error("Video not uploaded");
    if (video) {
      onUpdate(values);
    } else {
      upsert({
        ...values,
        ...mux,
        athletes: selectedAthletes?.map((athlete) => ({
          athleteId: athlete.athleteId,
          name: athlete.name ?? "unknown",
          isHp: athlete.isHp,
        })),
        tags: selectedTags,
        competition: selectedCompetition,
        newVideo: true,
      });
    }
  };

  const onUpdate = (values: FormProps) => {
    if (!mux?.muxPlaybackId) return toast.error("Video not uploaded");
    const thumbnailUrl = generateVideoThumbnail({
      playbackId: mux?.muxPlaybackId,
      params: {
        time: +(values.time ?? 1),
        width: values.width ? +values.width : undefined,
        height: values.height ? +values.height : undefined,
        rotate: values.rotate ? +values.rotate : undefined,
        fit_mode: values.fit_mode ? values.fit_mode : undefined,
      },
    });

    setSelectedAthletes(selectedAthletes);

    upsert({
      id: video!.id,
      ...mux,
      title: values.title,
      thumbnail: thumbnailUrl,
      sport: values.sport,
      permission: values.permission,
      status: values.status,
      athletes: selectedAthletes?.map((athlete) => ({
        athleteId: athlete.athleteId,
        name: athlete.name ?? "unknown",
        isHp: athlete.isHp,
      })),
      tags: selectedTags,
      competition: selectedCompetition,
      newVideo: mux.fullFilename !== video?.fullFilename,
      videoDate: values.videoDate,
    });
  };

  const videoSport = form.watch("sport");

  const onSetThumbnail = (time: number) => {
    form.setValue("time", time.toString());
  };

  const onTagClick = (tag: string) => {
    if (!selectedTags) return;
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((t) => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const thumbnailTime = (+(form.watch("time") ?? 0)).toFixed(2);

  return (
    <div className="grid w-full gap-4 md:gap-6 md:p-4 md:px-12 lg:grid-cols-3 lg:gap-10">
      <div className="grid h-fit w-full gap-2">
        <UploadButton
          label="Upload Video"
          setVideoPauseTime={video && setVideoPauseTime}
          mux={mux}
          setMux={setMux}
          showFilename={false}
          videoId={video?.id}
        />
        {/* Video Info put below */}
        {video && (
          <div className="border-green-500 flex flex-col gap-1 border text-xs md:text-sm">
            <p className="font-bold">{video?.title}</p>
            <div className="flex">
              <p className="font-bold">Sport:&nbsp;</p>
              {video?.sport}
            </div>
            <div className="flex">
              <p className="font-bold">Status:&nbsp;</p>
              {video?.status?.replaceAll("_", " ")}
            </div>
            {/* athletes */}
            {video.athletes.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <p className="font-bold">Athlete:&nbsp;</p>
                <p>
                  {isExpanded
                    ? video.athletes.map((athlete) => athlete.name).join(", ")
                    : video.athletes
                        .slice(0, 3)
                        .map((athlete) => athlete.name)
                        .join(", ")}
                </p>
                <button
                  onClick={toggleExpand}
                  className="text-orange hover:text-orange/80"
                >
                  {isExpanded ? "Show Less" : "Show More"}
                </button>
              </div>
            )}
            {/* Competition */}
            {video.competition && (
              <div className="">
                <p className="font-bold">
                  Competition: {video.competition.date}
                </p>
                <div className="flex">
                  <p>{video.competition?.name}</p>
                  <p>{video.competition?.location}</p>
                </div>
              </div>
            )}
            {/* metadata */}
            <div className="flex flex-row gap-2">
              {video.sport && <Tag label={video.sport} />}
              {video.tags.map((tag) => (
                <div key={tag.text} className="font-bold">
                  <Tag label={tag.text} />
                </div>
              ))}
            </div>
            {/* linked videos */}
            {linkedVideos.length > 0 && (
              <div className="flex flex-col gap-1">
                <p className="font-bold">Linked Videos:</p>
                <div className="flex flex-wrap gap-1">
                  {linkedVideos.map((linkedVideo) => (
                    <div
                      key={linkedVideo.id}
                      className="flex items-center gap-1 rounded bg-gray-700 px-2 py-1"
                    >
                      <p className="text-xs text-white">{linkedVideo.title}</p>
                      <span className="text-xs text-gray-400">
                        ({linkedVideo.relationshipType})
                      </span>
                      <button
                        onClick={() =>
                          removeRelationship({
                            parentId: video.id,
                            childId: linkedVideo.id,
                          })
                        }
                        className="ml-1 rounded-full p-0.5 transition-colors hover:bg-gray-600"
                      >
                        <X className="h-3 w-3 text-gray-400 hover:text-white" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Child video warning */}
        {isChildVideo && (
          <div className="rounded-md border border-yellow-500/20 bg-yellow-500/10 p-4 text-yellow-600 dark:text-yellow-400">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium">
                  Child Video - Limited Editing
                </h3>
                <p className="mt-1 text-sm">
                  This video is a child video and inherits competition, sport,
                  permission, and athletes from its parent video. You can edit
                  the title and tags/metadata independently, but other
                  properties are automatically managed by the parent-child
                  relationship.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="col-span-2 w-full md:pt-12">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="grid w-full gap-6 md:grid-cols-2"
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Video title..."
                      type="text"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <CompetitionSelect
              sport={videoSport}
              selectedCompetition={selectedCompetition}
              setSelectedCompetition={setSelectedCompetition}
              disabled={isChildVideo}
            />
            <FormField
              control={form.control}
              name="sport"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sport</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setSelectedCompetition(undefined);
                      setSelectedTags([]);
                      setSelectedAthletes([]);
                    }}
                    defaultValue={field.value}
                    disabled={isChildVideo}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a sport..." />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {roleSports.map((item) => (
                        <SelectItem value={item} key={item}>
                          <div className="capitalize">
                            {item.replaceAll("_", " ")}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <AthleteSelect
              sport={videoSport}
              setSelectedAthletes={setSelectedAthletes}
              selectedAthletes={selectedAthletes}
            />

            <FormField
              control={form.control}
              name="permission"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Permission</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                    }}
                    defaultValue={field.value}
                    disabled={isChildVideo}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select permission..." />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {videoPermissions.map((item) => (
                        <SelectItem value={item} key={item}>
                          <div className="capitalize">{item}</div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {videoSport && (
              <Metadata
                sport={videoSport}
                selectedTags={selectedTags}
                // setSelectedTags={setSelectedTags}
                onTagClick={onTagClick}
              />
            )}
            <FormField
              control={form.control}
              name="videoDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Video Date</FormLabel>
                  <VideoDate selected={field.value} onSelect={field.onChange} />
                  <FormMessage />
                </FormItem>
              )}
            />

            {video && (
              <div className="grid gap-3">
                <Label className="pl-1">Thumbnail time {thumbnailTime}</Label>
                <Button
                  variant="secondary"
                  id="time"
                  type="button"
                  className="w-fit min-w-[50%]"
                  onClick={() => onSetThumbnail(videoPauseTime)}
                  disabled={isChildVideo}
                >
                  Set thumbnail ({videoPauseTime.toFixed(2)})
                </Button>
              </div>
            )}
            {video && <div />}
            <Button
              variant="primary"
              type="submit"
              loading={isPendingUpdate || isPendingUpdate}
              className="w-1/2"
            >
              {video ? "Update" : "Submit"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
};
