"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { toast } from "react-hot-toast";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { api } from "~/trpc/react";
import { MultiSelect } from "./MultiSelect";

interface Video {
  id: string;
  title: string;
  filename: string;
}

interface VideoRelationship {
  video: Video;
  relationType: string;
}

interface LinkVideosModalProps {
  trigger?: React.ReactNode;
  disabled?: boolean;
}

export const LinkVideosModal = ({
  trigger,
  disabled,
}: LinkVideosModalProps) => {
  const [open, setOpen] = useState(false);
  const [selectedVideos, setSelectedVideos] = useState<VideoRelationship[]>([]);
  const params = useParams();
  const parentId = params.vid as string;

  // Get the current video's sport
  const { data: currentVideoSport } = api.video.getVideoSport.useQuery(
    { videoId: parentId },
    { enabled: !!parentId },
  );

  // Get videos by sport (excluding the current video)
  const { data: videosBySport = [], isFetching: isLoadingVideosBySport } =
    api.video.getVideosBySport.useQuery(
      { sport: currentVideoSport! },
      { enabled: !!currentVideoSport },
    );

  // Get already linked videos
  const { data: linkedVideosData } = api.video.getLinkedVideos.useQuery(
    {
      parentId: parentId,
    },
    { enabled: !!parentId },
  );

  const linkedVideos = linkedVideosData?.videoList ?? [];

  // Filter out the current video from the lister types
  const availableVideos = videosBySport
    .filter((video) => video.id !== parentId)
    .map((video) => ({
      id: video.id,
      title: video.title ?? "",
      filename: video.filename ?? "",
    }));

  const initializeSelectedVideos = () => {
    const linkedVideoRelationships = linkedVideos.map((video) => ({
      video: {
        id: video.id,
        title: video.title ?? "",
        filename: video.filename ?? "",
      },
      relationType: video.relationshipType ?? "",
    }));
    setSelectedVideos(linkedVideoRelationships);
  };

  useEffect(() => {
    if (linkedVideos.length > 0 && selectedVideos.length === 0) {
      initializeSelectedVideos();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [linkedVideos]);

  const { mutate: createRelationship, isPending } =
    api.video.createVideoRelationship.useMutation({
      onSuccess: () => {
        toast.success("Videos linked successfully");
        setOpen(false);
        setSelectedVideos([]);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to link videos");
      },
    });

  const handleVideoSelection = (videos: Video[]) => {
    // Keep existing relationships for videos that are still selected
    const existingRelationships = selectedVideos.filter((sv) =>
      videos.some((v) => v.id === sv.video.id),
    );

    // Add new videos with empty relation types
    const newVideos = videos
      .filter((video) => !selectedVideos.some((sv) => sv.video.id === video.id))
      .map((video) => ({
        video,
        relationType: "",
      }));

    setSelectedVideos([...existingRelationships, ...newVideos]);
  };

  const handleRelationTypeChange = (videoId: string, relationType: string) => {
    setSelectedVideos((prev) =>
      prev.map((item) =>
        item.video.id === videoId ? { ...item, relationType } : item,
      ),
    );
  };

  const onSubmit = () => {
    if (selectedVideos.length === 0) {
      toast.error("Please select at least one video to link");
      return;
    }

    // Validate that all selected videos have relation types
    const invalidVideos = selectedVideos.filter((v) => !v.relationType.trim());
    if (invalidVideos.length > 0) {
      toast.error("Please enter relationship types for all selected videos");
      return;
    }

    createRelationship({
      parentId,
      relationships: selectedVideos.map((v) => ({
        childId: v.video.id,
        relationType: v.relationType,
      })),
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild disabled={disabled}>
        {trigger ?? (
          <Button variant="primary" size="sm" disabled={disabled}>
            Link Videos
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="rounded-md border border-black bg-primary shadow-xl sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">
            Link Videos
          </DialogTitle>
          <DialogDescription className="text-white/70">
            Link this video (parent) to other videos (children) with the same
            sport.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Select Videos to Link</Label>
            {isLoadingVideosBySport ? (
              <div className="text-sm text-gray-400">Loading videos...</div>
            ) : currentVideoSport ? (
              <MultiSelect
                videos={availableVideos.map((video) => {
                  const selectedVideo = selectedVideos.find(
                    (sv) => sv.video.id === video.id,
                  );
                  return {
                    ...video,
                    relationType: selectedVideo?.relationType,
                  };
                })}
                selectedVideos={selectedVideos.map((sv) => sv.video)}
                onSelectionChange={handleVideoSelection}
                placeholder={`Select videos from ${currentVideoSport}...`}
              />
            ) : (
              <div className="text-sm text-gray-400">
                Unable to load video sport
              </div>
            )}
            {availableVideos.length === 0 && currentVideoSport && (
              <p className="text-xs text-gray-400">
                No other videos found in {currentVideoSport} sport.
              </p>
            )}
          </div>

          {selectedVideos.length > 0 && (
            <div className="space-y-3">
              <Label>Set Relationship Types</Label>
              <div className="max-h-60 space-y-2 overflow-y-auto">
                {selectedVideos.map((item) => (
                  <div
                    key={item.video.id}
                    className="flex items-center gap-2 rounded-md border border-black  bg-grey p-3"
                  >
                    <div className="min-w-0 flex-1">
                      <div className="truncate text-sm font-medium text-white">
                        {item.video.title || item.video.filename}
                      </div>
                      <div className="text-xs text-gray-400">
                        ID: {item.video.id}
                      </div>
                    </div>
                    <Input
                      value={item.relationType}
                      onChange={(e) =>
                        handleRelationTypeChange(item.video.id, e.target.value)
                      }
                      placeholder="e.g., sync, duplicate"
                      className="flex-1 rounded-md border-2 border-black bg-grey text-white focus:border-orange focus:ring-2 focus:ring-blue-200"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="default"
              onClick={() => {
                setOpen(false);
                setSelectedVideos([]);
              }}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={onSubmit}
              disabled={isPending || selectedVideos.length === 0}
              variant="primary"
            >
              {isPending
                ? "Linking..."
                : `Link ${selectedVideos.length} Video${selectedVideos.length !== 1 ? "s" : ""}`}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};
