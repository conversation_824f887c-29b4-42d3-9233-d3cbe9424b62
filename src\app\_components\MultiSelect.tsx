"use client";

import { useState } from "react";
import { Check, ChevronsUpDown, X } from "lucide-react";
import { cn } from "~/lib/utils";
import { Button } from "~/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";

interface Video {
  id: string;
  title: string;
  filename: string;
  relationType?: string;
}

interface MultiSelectProps {
  videos: Video[];
  selectedVideos: Video[];
  onSelectionChange: (videos: Video[]) => void;
  placeholder?: string;
}

export const MultiSelect = ({
  videos,
  selectedVideos,
  onSelectionChange,
  placeholder = "Select videos...",
}: MultiSelectProps) => {
  const [open, setOpen] = useState(false);

  const handleSelect = (video: Video) => {
    const isSelected = selectedVideos.some((v) => v.id === video.id);
    if (isSelected) {
      onSelectionChange(selectedVideos.filter((v) => v.id !== video.id));
    } else {
      onSelectionChange([...selectedVideos, video]);
    }
  };

  const handleRemove = (videoId: string) => {
    onSelectionChange(selectedVideos.filter((v) => v.id !== videoId));
  };

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="default"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedVideos.length === 0
              ? placeholder
              : `${selectedVideos.length} video(s) selected`}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search videos..." className="h-9" />
            <CommandList>
              <CommandEmpty>No videos found.</CommandEmpty>
              <CommandGroup>
                {videos.map((video) => {
                  const isSelected = selectedVideos.some(
                    (v) => v.id === video.id,
                  );
                  return (
                    <CommandItem
                      key={video.id}
                      onSelect={() => handleSelect(video)}
                      className="cursor-pointer"
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          isSelected ? "opacity-100" : "opacity-0",
                        )}
                      />
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {video.title || video.filename}
                        </span>
                        <span className="text-xs text-gray-400">
                          ID: {video.id}
                        </span>
                        {video.relationType && (
                          <span className="text-xs text-blue-400">
                            Type: {video.relationType}
                          </span>
                        )}
                        {video.title && video.title !== video.filename && (
                          <span className="text-xs text-gray-500">
                            {video.filename}
                          </span>
                        )}
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
                
      {selectedVideos.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedVideos.map((video) => (
            <div
              key={video.id}
              className="flex items-center gap-1 rounded-md bg-gray-600 px-2 py-1 text-sm text-white transition-colors hover:bg-gray-500"
            >
              <span className="max-w-[200px] truncate">
                {video.title || video.filename}
              </span>
              <span className="ml-1 text-xs text-gray-300">
                ({video.id.slice(0, 8)}...)
              </span>
              <button
                onClick={() => handleRemove(video.id)}
                className="ml-1 rounded-full p-0.5 transition-colors hover:bg-gray-400"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
